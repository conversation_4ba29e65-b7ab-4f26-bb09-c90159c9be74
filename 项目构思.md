项目名为KeyHub，用于管理Ai平台的密钥。主要功能为对话检查进行KEY测活与部分提供商的余额查询。

## 功能

### 对话检查

通过对话检查，可以对Ai平台的密钥进行测活，发送hi信息判断是否能够对话，测活结果包括：
   - 有效
   - 无效


### 余额查询

部分提供商存在余额查询接口，可以查询余额。对于不同提供商存在不同的货币，暂有三种：
   - USD （$）
   - CNY （￥）
   - Token （T）

部分提供商存在总余额、赠送余额、充值余额。如硅基流动
```
curl --request GET \
  --url https://api.siliconflow.cn/v1/user/info \
  --header 'Authorization: Bearer <token>'
```
响应：
```
{
  "data": {
    "balance": "0.88",
    "introduction": "",
    "chargeBalance": "88.00",
    "totalBalance": "88.88",
    ...
  },
  ...
}
```
totalBalance为总余额，chargeBalance为充值余额，balance为赠送余额。

## 项目UI需求

项目需要一个管理UI，可以使用Typescript。一个顶栏，内容区分为三个：
   - 控制台：显示总体信息，如Key个数等
   - Key管理：显示Key列表，可以进行测活、余额查询等操作
   - 设置：设置项目信息，如密钥提供商等

对于Key管理，在列表中可以选择显示的字段，如：
   - ID
   - 提供商
   - Key
   - 状态（图标显示，鼠标悬停显示具体信息，如测试时间，测试状态）
   - 总余额 （鼠标悬停显示具体信息，如查询余额时间）
   - 充值余额 （默认不显示）
   - 赠送余额 （默认不显示）
   - 备注
   - 操作（测活、余额查询、编辑、删除，仅显示图标）
并且可以进行排序。
需要几个按钮：
   - 导入Key
   - 导出Key
   - 测试Key
   - 查询余额
   - 编辑Key
   - 删除Key

